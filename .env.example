# Solar Classifier API Configuration
# Copy this file to .env and update with your values

# Environment
FLASK_ENV=production

# Model Configuration
# IMPORTANT: Change this to your model filename
MODEL_FILENAME=final_google_classifier.pth
MODEL_DEVICE=cpu  # Options: cpu, cuda, mps

# API Security
API_KEY=your-secure-api-key-here

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Logging
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR

# Cache Settings
MAX_CACHE_SIZE_MB=10000  # 10GB
CACHE_CLEANUP_DAYS=30

# Performance
REQUEST_TIMEOUT=30
MAX_BATCH_SIZE=50

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090