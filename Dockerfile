# Build stage
FROM python:3.11-slim as builder

RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Runtime stage  
FROM python:3.11-slim

# Copy only the installed packages from builder
COPY --from=builder /root/.local /root/.local

WORKDIR /app

# Add local bin to PATH
ENV PATH=/root/.local/bin:$PATH

# Copy application code
COPY . .

# Fixed: Use 'application' instead of 'app' to match wsgi.py
# HTTP/2 compatible configuration for Cloud Run
CMD exec gunicorn --bind :$PORT --workers 1 --threads 8 --worker-class gthread --timeout 60 --preload --keep-alive 2 --max-requests 1000 --max-requests-jitter 50 wsgi:application