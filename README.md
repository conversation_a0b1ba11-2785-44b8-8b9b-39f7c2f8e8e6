# Solar Classifier

## Prerequisites

- Python 3.11+
- Docker
- Google Cloud CLI (gcloud)
- Access to Google Cloud project: `admin-solarreviews`

## Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd solar-classifier
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run locally**
   ```bash
   python wsgi.py
   ```

## Google Cloud Authentication & Setup

### 1. Install Google Cloud CLI

**macOS (using Homebrew):**
```bash
brew install google-cloud-sdk
```

### 2. Authenticate with G<PERSON>:
```bash
# Login to your Google account
gcloud auth login

# Set the project ID
gcloud config set project admin-solarreviews

# Verify your configuration
gcloud config list
```

### 3. Configure Application Default Credentials

```bash
# Set up application default credentials
gcloud auth application-default login

# Verify the setup
gcloud auth application-default print-access-token
```

### 4. Enable Required APIs

```bash
# Enable Cloud Build API
gcloud services enable cloudbuild.googleapis.com

# Enable Cloud Run API
gcloud services enable run.googleapis.com
```

## Required GCP IAM Permissions

To successfully deploy the application, your Google Cloud account needs the following IAM roles and permissions. These are the minimum required permissions following the principle of least privilege:

### Essential IAM Roles

1. **Cloud Run Developer** (`roles/run.developer`)
   - Create, update, and delete Cloud Run services
   - Manage service configurations and traffic
   - More restrictive than Cloud Run Admin

2. **Cloud Build Service Account** (`roles/cloudbuild.builds.builder`)
   - Submit builds to Cloud Build
   - Access to build logs and build history
   - More restrictive than Cloud Build Editor

3. **Service Account User** (`roles/iam.serviceAccountUser`)
   - Ability to act as the Cloud Build service account
   - Required for Cloud Build to deploy to Cloud Run
   - Cannot be further restricted for this use case

4. **Storage Object Creator** (`roles/storage.objectCreator`)
   - Create objects in Cloud Storage buckets
   - Required for storing build artifacts and container images
   - More restrictive than Storage Admin/Object Admin

### Project-Level Permissions

Ensure you have access to the `admin-solarreviews` project with at least the following permissions:

```bash
# Check your current permissions
gcloud projects get-iam-policy admin-solarreviews --flatten="bindings[].members" --format="table(bindings.role)" --filter="bindings.members:$(gcloud config get-value account)"

# Check if you have the required roles
gcloud projects get-iam-policy admin-solarreviews --flatten="bindings[].members" --format="table(bindings.role)" --filter="bindings.members:$(gcloud config get-value account)" | grep -E "(run\.developer|cloudbuild\.builds\.builder|iam\.serviceAccountUser|storage\.objectCreator)"
```

### Granting Permissions (for Project Admins)

If you need to grant these permissions to a user:

```bash
# Grant Cloud Run Developer role
gcloud projects add-iam-policy-binding admin-solarreviews \
    --member="user:<EMAIL>" \
    --role="roles/run.developer"

# Grant Cloud Build Service Account role
gcloud projects add-iam-policy-binding admin-solarreviews \
    --member="user:<EMAIL>" \
    --role="roles/cloudbuild.builds.builder"

# Grant Service Account User role
gcloud projects add-iam-policy-binding admin-solarreviews \
    --member="user:<EMAIL>" \
    --role="roles/iam.serviceAccountUser"

# Grant Storage Object Creator role
gcloud projects add-iam-policy-binding admin-solarreviews \
    --member="user:<EMAIL>" \
    --role="roles/storage.objectCreator"
```

### Verifying Permissions

Before deploying, verify you have the necessary permissions:

```bash
# Test Cloud Run access
gcloud run services list --region=us-central1

# Test Cloud Build access
gcloud builds list --limit=1

# Test Storage access
gsutil ls gs://
```

If any of these commands fail with permission errors, contact your project administrator to grant the required roles.

## Deployment

### Environment Options

- **dev**: Deploys to `qa-sr-development` service (development environment)
- **prod**: Deploys to `qa-sr-production` service (production environment)

### Deployment Commands

**Deploy to Development:**
```bash
./deploy.sh dev
```

**Deploy to Production:**
```bash
./deploy.sh prod
```

### What the Deployment Script Does

1. Sets the Google Cloud project to `admin-solarreviews`
2. Enables required APIs (Cloud Build, Cloud Run)
3. Configures environment-specific settings:
   - **Dev**: 512Mi memory, max 10 instances, debug mode enabled
   - **Prod**: 1Gi memory, max 100 instances, debug mode disabled
4. Deploys the containerized application to Cloud Run
5. Provides the service URL upon completion

### Manual Deployment (Alternative)

If you prefer to deploy manually:

```bash
# Set the project
gcloud config set project admin-solarreviews

# Deploy to Cloud Run
gcloud run deploy <service-name> \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory <memory> \
    --max-instances <max-instances> \
    --set-env-vars FLASK_ENV=<environment> \
    --quiet
```

## Service Configuration

### Development Environment
- **Service Name**: `qa-sr-development`
- **Memory**: 512Mi
- **Max Instances**: 10
- **Environment**: Development with debug enabled

### Production Environment
- **Service Name**: `qa-sr-production`
- **Memory**: 1Gi
- **Max Instances**: 100
- **Environment**: Production with debug disabled

## API Endpoints

The service exposes a Flask API with the following endpoints:

- **Health Check**: `GET /health`
- **Classification**: `POST /classify` (for solar panel image classification)

## Monitoring & Logs

```bash
# View service logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=<service-name>"

# View service details
gcloud run services describe <service-name> --region=us-central1
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   # Re-authenticate
   gcloud auth login
   gcloud auth application-default login
   ```

2. **Permission Denied**
   - Ensure you have the necessary IAM roles (Cloud Run Admin, Cloud Build Editor)
   - Contact your project administrator

3. **Container Startup Issues**
   - **Error**: `Failed to find attribute 'app' in 'app'`
   - **Solution**: Fixed Dockerfile to use `wsgi:application` instead of `wsgi:app`
   - **Status**: ✅ RESOLVED - Container should now start successfully

4. **Model Loading Issues**
   - **Error**: Service fails to start due to missing model files
   - **Solution**: Service now starts gracefully with warnings about missing models
   - **Status**: ✅ RESOLVED - Service starts even without model files

3. **API Not Enabled**
   ```bash
   # Enable required APIs
   gcloud services enable cloudbuild.googleapis.com run.googleapis.com
   ```

### Getting Help

- Check the deployment logs for error messages
- Verify your gcloud configuration with `gcloud config list`
- Ensure you have the correct project permissions

## Project Structure

```
solar-classifier/
├── app/                    # Flask application code
├── config/                 # Configuration files
├── models/                 # ML model files
├── scripts/                # Utility scripts
├── deploy.sh              # Deployment script
├── Dockerfile             # Container configuration
├── requirements.txt       # Python dependencies
└── wsgi.py               # WSGI entry point
```