"""
Production Solar Classifier Flask API

IMPORTANT: Model Loading Configuration
=====================================
The model file is loaded from: models/final_google_classifier.pth

To change the model:
1. Upload new model file to the 'models' directory
2. Update MODEL_FILENAME in .env file or config.py
3. Restart the API service

Current model: final_google_classifier.pth
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import torch
import torch.nn as nn
from torchvision import transforms
import numpy as np
import time
import logging
import traceback
from pathlib import Path
import os

from app.models import SolarClassificationModel
from app.config import get_config
from app.utils import (
    GoogleMapsImageExtractor,
    verify_api_key,
    format_classification_response,
    validate_coordinates
)

# ============================================================================
# INITIALIZATION
# ============================================================================

# Load configuration
config = get_config()

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(config.LOG_DIR / 'api.log')
    ]
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)

# Initialize rate limiter
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=[
        f"{config.RATE_LIMIT_PER_MINUTE} per minute",
        f"{config.RATE_LIMIT_PER_HOUR} per hour"
    ]
)

# Global variables for model and extractor
MODEL = None
IMAGE_EXTRACTOR = None
MODEL_LOAD_TIME = None
MODEL_LOAD_ERROR = None
IMAGE_EXTRACTOR_ERROR = None

# Image preprocessing transforms
TRANSFORMS = transforms.Compose([
    transforms.ToPILImage(),
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])


# ============================================================================
# MODEL LOADING SECTION - CHANGE MODEL FILE HERE
# ============================================================================

def load_model():
    """
    Load the trained model from the models directory
    
    IMPORTANT: To change the model file:
    1. Upload new .pth file to models/ directory
    2. Update MODEL_FILENAME in config or .env
    3. Restart the service
    """
    global MODEL, IMAGE_EXTRACTOR, MODEL_LOAD_TIME, MODEL_LOAD_ERROR, IMAGE_EXTRACTOR_ERROR
    
    try:
        # ===== MODEL FILE CONFIGURATION =====
        # This is where the model file is specified
        model_filename = config.MODEL_FILENAME  # Default: 'final_google_classifier.pth'
        model_path = config.MODEL_PATH  # This will be: models/final_google_classifier.pth
        
        logger.info("="*60)
        logger.info("LOADING SOLAR CLASSIFICATION MODEL")
        logger.info("="*60)
        logger.info(f"Model file: {model_filename}")
        logger.info(f"Full path: {model_path}")
        logger.info(f"Google API Key configured: {'Yes' if config.GOOGLE_API_KEY else 'No'}")
        logger.info(f"API Key configured: {'Yes' if config.API_KEY != 'your-secure-api-key' else 'No'}")
        logger.info("="*60)
        
        # Check if model file exists
        if not model_path.exists():
            logger.error(f"Model file not found: {model_path}")
            logger.error(f"Please ensure '{model_filename}' is in the 'models' directory")
            
            # List available model files
            available_models = list(config.MODEL_DIR.glob('*.pth'))
            if available_models:
                logger.info("Available model files:")
                for model_file in available_models:
                    logger.info(f"  - {model_file.name}")
            else:
                logger.error("No .pth files found in models directory!")
            
            return False
        
        # Initialize model architecture
        logger.info("Initializing model architecture...")
        MODEL = SolarClassificationModel(
            num_detailed_classes=config.NUM_CLASSES,
            dropout_rate=0.3
        )
        
        # Load model weights
        logger.info(f"Loading model weights from {model_filename}...")
        device = torch.device(config.MODEL_DEVICE)
        
        # Handle pathlib._local issue by creating a mock module if needed
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        except Exception as e:
            if "pathlib._local" in str(e):
                # Create a temporary mock pathlib._local module
                import sys
                import types
                from pathlib import PosixPath, WindowsPath
                
                # Create and register the mock module
                pathlib_local = types.ModuleType('pathlib._local')
                pathlib_local.PosixPath = PosixPath
                pathlib_local.WindowsPath = WindowsPath
                sys.modules['pathlib._local'] = pathlib_local
                
                # Try loading again
                checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            else:
                raise
        
        # Load state dict
        MODEL.load_state_dict(checkpoint['model_state_dict'])
        MODEL = MODEL.to(device)
        MODEL.eval()
        MODEL_LOAD_ERROR = None
        
        # Log model information
        logger.info("Model loaded successfully!")
        logger.info(f"Device: {device}")
        logger.info(f"Model epoch: {checkpoint.get('epoch', 'unknown')}")
        logger.info(f"Model accuracy: {checkpoint.get('detailed_accuracy', 0):.2f}%")
        logger.info(f"Number of classes: {config.NUM_CLASSES}")
        logger.info(f"Classes: {MODEL.detailed_classes}")
        
        # Initialize Google Maps image extractor
        logger.info("Initializing Google Maps image extractor...")
        try:
            IMAGE_EXTRACTOR = GoogleMapsImageExtractor(
                api_key=config.GOOGLE_API_KEY,
                cache_dir=config.CACHE_DIR,
                config=config
            )
            IMAGE_EXTRACTOR_ERROR = None
        except Exception as img_e:
            logger.error(f"Failed to initialize Google Maps extractor: {img_e}")
            logger.error(traceback.format_exc())
            IMAGE_EXTRACTOR_ERROR = str(img_e)
            raise
        
        MODEL_LOAD_TIME = time.time()
        logger.info("="*60)
        logger.info("MODEL LOADING COMPLETE - API READY!")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize service: {e}")
        logger.error(traceback.format_exc())
        
        # Store specific error for debugging
        if MODEL is None:
            MODEL_LOAD_ERROR = str(e)
        if IMAGE_EXTRACTOR is None:
            IMAGE_EXTRACTOR_ERROR = str(e)
            
        return False


# ============================================================================
# CLASSIFICATION LOGIC
# ============================================================================

def classify_location(latitude, longitude):
    """Classify a single location"""
    if MODEL is None or IMAGE_EXTRACTOR is None:
        raise RuntimeError("Model or image extractor not loaded")
    
    # Extract image
    image_data = IMAGE_EXTRACTOR.extract_image(latitude, longitude)
    
    if image_data is None:
        raise ValueError("Failed to extract Google Maps image")
    
    # Run inference
    MODEL.eval()
    with torch.no_grad():
        # Preprocess image
        input_tensor = TRANSFORMS(image_data)
        input_batch = input_tensor.unsqueeze(0).to(config.MODEL_DEVICE)
        
        # Get predictions
        detailed_output, binary_output = MODEL(input_batch)
        
        # Process predictions
        detailed_probs = torch.softmax(detailed_output, dim=1)
        detailed_pred = torch.argmax(detailed_probs, dim=1).item()
        detailed_confidence = detailed_probs[0, detailed_pred].item()
        
        binary_prob = torch.sigmoid(binary_output).item()
        binary_pred = 1 if binary_prob > 0.5 else 0
        
        # Get class names
        detailed_class = MODEL.detailed_classes[detailed_pred]
        binary_class = MODEL.binary_classes[binary_pred]
        
        result = {
            'detailed_class': detailed_class,
            'detailed_confidence': detailed_confidence,
            'binary_class': binary_class,
            'binary_probability': binary_prob,
            'solar_suitable': binary_class == 'pass',
            'all_probabilities': {
                MODEL.detailed_classes[i]: detailed_probs[0, i].item() 
                for i in range(len(MODEL.detailed_classes))
            }
        }
        
        logger.info(f"Classification result: {result}")
        return result


# ============================================================================
# API ENDPOINTS
# ============================================================================

@app.route('/', methods=['GET'])
def index():
    """API documentation endpoint"""
    return jsonify({
        'name': config.API_TITLE,
        'version': config.API_VERSION,
        'description': config.API_DESCRIPTION,
        'model': {
            'file': config.MODEL_FILENAME,
            'loaded': MODEL is not None,
            'device': config.MODEL_DEVICE,
            'classes': MODEL.detailed_classes if MODEL else None
        },
        'endpoints': {
            'GET /': 'API documentation',
            'GET /health': 'Health check',
            'GET /status': 'Detailed initialization status',
            'GET /stats': 'API statistics',
            'POST /classify': 'Classify single location',
            'POST /classify-batch': 'Classify multiple locations'
        },
        'rate_limits': {
            'per_minute': config.RATE_LIMIT_PER_MINUTE,
            'per_hour': config.RATE_LIMIT_PER_HOUR
        }
    })


@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint - optimized for HTTP/2"""
    uptime = time.time() - MODEL_LOAD_TIME if MODEL_LOAD_TIME else 0

    response_data = {
        'status': 'healthy' if MODEL is not None else 'unhealthy',
        'model_loaded': MODEL is not None,
        'image_extractor_loaded': IMAGE_EXTRACTOR is not None,
        'model_file': config.MODEL_FILENAME,
        'uptime_seconds': round(uptime, 2),
        'timestamp': time.time(),
        'protocol': 'HTTP/2'
    }

    response = jsonify(response_data)
    # Add headers for better HTTP/2 compatibility
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Connection'] = 'keep-alive'
    return response


@app.route('/status', methods=['GET'])
def status():
    """Detailed initialization status endpoint"""
    return jsonify({
        'model': {
            'loaded': MODEL is not None,
            'error': MODEL_LOAD_ERROR,
            'file': config.MODEL_FILENAME,
            'path': str(config.MODEL_PATH),
            'exists': config.MODEL_PATH.exists() if config.MODEL_PATH else False,
            'device': config.MODEL_DEVICE,
            'num_classes': config.NUM_CLASSES
        },
        'image_extractor': {
            'loaded': IMAGE_EXTRACTOR is not None,
            'error': IMAGE_EXTRACTOR_ERROR,
            'google_api_key_set': bool(config.GOOGLE_API_KEY),
            'cache_dir': str(config.CACHE_DIR)
        },
        'overall_status': 'ready' if (MODEL is not None and IMAGE_EXTRACTOR is not None) else 'not_initialized',
        'timestamp': time.time()
    })


@app.route('/stats', methods=['GET'])
def stats():
    """API statistics endpoint"""
    # Verify API key
    is_valid, error = verify_api_key(request, config)
    if not is_valid:
        return jsonify({'error': error}), 401
    
    if IMAGE_EXTRACTOR is None:
        return jsonify({
            'error': 'Google Maps extractor not initialized',
            'details': IMAGE_EXTRACTOR_ERROR or 'Image extractor not available'
        }), 503
    
    return jsonify({
        'google_maps_stats': IMAGE_EXTRACTOR.get_stats(),
        'model': {
            'file': config.MODEL_FILENAME,
            'device': str(config.MODEL_DEVICE),
            'classes': MODEL.detailed_classes if MODEL else None
        },
        'cache': {
            'directory': str(config.CACHE_DIR),
            'max_size_mb': config.MAX_CACHE_SIZE_MB
        }
    })


@app.route('/classify', methods=['POST'])
def classify():
    """Single location classification endpoint"""
    try:
        # Debug API key verification
        received_api_key_header = request.headers.get('X-API-Key')
        received_api_key_json = request.json.get('api_key', '') if request.json else ''
        expected_api_key = config.API_KEY
        
        # Verify API key
        is_valid, error = verify_api_key(request, config)
        if not is_valid:
            from app.config import ENV_LOAD_DEBUG
            return jsonify({
                'error': error,
                'debug': {
                    'expected_key': expected_api_key,
                    'received_key_header': received_api_key_header,
                    'received_key_json': received_api_key_json,
                    'headers': dict(request.headers),
                    'config_api_key_length': len(expected_api_key) if expected_api_key else 0,
                    'api_key_from_env': config.API_KEY != 'your-secure-api-key',
                    'env_loading': ENV_LOAD_DEBUG,
                    'all_env_vars': {k: v[:10] + '...' if len(v) > 10 else v for k, v in os.environ.items() if 'API_KEY' in k}
                }
            }), 401
        
        # Check if model is loaded
        if MODEL is None and IMAGE_EXTRACTOR is None:
            from app.config import ENV_LOAD_DEBUG
            return jsonify({
                'error': 'Service not initialized',
                'details': 'Both model and image extractor failed to initialize',
                'model_error': MODEL_LOAD_ERROR,
                'image_extractor_error': IMAGE_EXTRACTOR_ERROR,
                'debug': {
                    'model_file': config.MODEL_FILENAME,
                    'model_path': str(config.MODEL_PATH),
                    'model_exists': config.MODEL_PATH.exists() if config.MODEL_PATH else False,
                    'google_api_key_set': bool(config.GOOGLE_API_KEY),
                    'google_api_key_length': len(config.GOOGLE_API_KEY) if config.GOOGLE_API_KEY else 0,
                    'cache_dir': str(config.CACHE_DIR),
                    'cache_dir_exists': os.path.exists(str(config.CACHE_DIR)),
                    'env_loading': ENV_LOAD_DEBUG,
                    'working_dir': os.getcwd(),
                    'models_dir_exists': os.path.exists('/app/models'),
                    'models_dir_contents': os.listdir('/app/models') if os.path.exists('/app/models') else 'Directory does not exist',
                    'base_dir_contents': os.listdir('/app') if os.path.exists('/app') else 'Directory does not exist'
                }
            }), 503
        elif MODEL is None:
            return jsonify({
                'error': 'Model not initialized',
                'details': MODEL_LOAD_ERROR or 'Model failed to load',
                'debug': {
                    'model_file': config.MODEL_FILENAME,
                    'model_path': str(config.MODEL_PATH),
                    'model_exists': config.MODEL_PATH.exists() if config.MODEL_PATH else False
                }
            }), 503
        elif IMAGE_EXTRACTOR is None:
            return jsonify({
                'error': 'Google Maps extractor not initialized',
                'details': IMAGE_EXTRACTOR_ERROR or 'Image extractor failed to load',
                'debug': {
                    'google_api_key_set': bool(config.GOOGLE_API_KEY),
                    'google_api_key_length': len(config.GOOGLE_API_KEY) if config.GOOGLE_API_KEY else 0,
                    'cache_dir': str(config.CACHE_DIR),
                    'cache_dir_exists': os.path.exists(str(config.CACHE_DIR)),
                    'image_extractor_error_detail': IMAGE_EXTRACTOR_ERROR
                }
            }), 503
        
        # Get request data
        data = request.json
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        
        if latitude is None or longitude is None:
            return jsonify({'error': 'Missing latitude or longitude'}), 400
        
        # Validate coordinates
        is_valid, error = validate_coordinates(latitude, longitude)
        if not is_valid:
            return jsonify({'error': error}), 400
        
        # Perform classification
        start_time = time.time()
        result = classify_location(float(latitude), float(longitude))
        processing_time = time.time() - start_time
        
        # Format response
        response = format_classification_response(
            result,
            {'latitude': latitude, 'longitude': longitude},
            processing_time,
            config
        )
        
        logger.info(f"Classified ({latitude}, {longitude}) -> {result['detailed_class']} "
                   f"({result['detailed_confidence']:.1%})")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Classification error: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'error': str(e),
            'model_file': config.MODEL_FILENAME
        }), 500


@app.route('/classify-batch', methods=['POST'])
def classify_batch():
    """Batch classification endpoint"""
    try:
        # Verify API key
        is_valid, error = verify_api_key(request, config)
        if not is_valid:
            return jsonify({'error': error}), 401
        
        # Check if model is loaded
        if MODEL is None and IMAGE_EXTRACTOR is None:
            from app.config import ENV_LOAD_DEBUG
            return jsonify({
                'error': 'Service not initialized',
                'details': 'Both model and image extractor failed to initialize',
                'model_error': MODEL_LOAD_ERROR,
                'image_extractor_error': IMAGE_EXTRACTOR_ERROR,
                'debug': {
                    'model_file': config.MODEL_FILENAME,
                    'model_path': str(config.MODEL_PATH),
                    'model_exists': config.MODEL_PATH.exists() if config.MODEL_PATH else False,
                    'google_api_key_set': bool(config.GOOGLE_API_KEY),
                    'env_loading': ENV_LOAD_DEBUG,
                    'working_dir': os.getcwd()
                }
            }), 503
        elif MODEL is None:
            return jsonify({
                'error': 'Model not initialized',
                'details': MODEL_LOAD_ERROR or 'Model failed to load',
                'debug': {
                    'model_file': config.MODEL_FILENAME,
                    'model_path': str(config.MODEL_PATH),
                    'model_exists': config.MODEL_PATH.exists() if config.MODEL_PATH else False
                }
            }), 503
        elif IMAGE_EXTRACTOR is None:
            return jsonify({
                'error': 'Google Maps extractor not initialized',
                'details': IMAGE_EXTRACTOR_ERROR or 'Image extractor failed to load',
                'debug': {
                    'google_api_key_set': bool(config.GOOGLE_API_KEY),
                    'google_api_key_length': len(config.GOOGLE_API_KEY) if config.GOOGLE_API_KEY else 0,
                    'cache_dir': str(config.CACHE_DIR),
                    'cache_dir_exists': os.path.exists(str(config.CACHE_DIR)),
                    'image_extractor_error_detail': IMAGE_EXTRACTOR_ERROR
                }
            }), 503
        
        # Get request data
        data = request.json
        coordinates_list = data.get('coordinates', [])
        
        if not coordinates_list:
            return jsonify({'error': 'No coordinates provided'}), 400
        
        if len(coordinates_list) > config.MAX_BATCH_SIZE:
            return jsonify({
                'error': f'Batch size too large (max {config.MAX_BATCH_SIZE})'
            }), 400
        
        # Process batch
        results = []
        start_time = time.time()
        
        for i, coords in enumerate(coordinates_list):
            try:
                # Validate coordinates
                is_valid, error = validate_coordinates(
                    coords.get('latitude'),
                    coords.get('longitude')
                )
                
                if not is_valid:
                    results.append({
                        'index': i,
                        'status': 'error',
                        'error': error,
                        'coordinates': coords
                    })
                    continue
                
                # Classify
                result = classify_location(
                    float(coords['latitude']),
                    float(coords['longitude'])
                )
                
                results.append({
                    'index': i,
                    'status': 'success',
                    'coordinates': coords,
                    'classification': {
                        'detailed_class': result['detailed_class'],
                        'detailed_confidence': round(result['detailed_confidence'] * 100, 1),
                        'binary_class': result['binary_class'],
                        'solar_suitable': result['solar_suitable']
                    }
                })
                
            except Exception as e:
                results.append({
                    'index': i,
                    'status': 'error',
                    'error': str(e),
                    'coordinates': coords
                })
        
        total_time = time.time() - start_time
        
        return jsonify({
            'status': 'success',
            'results': results,
            'batch_size': len(coordinates_list),
            'successful': sum(1 for r in results if r['status'] == 'success'),
            'failed': sum(1 for r in results if r['status'] == 'error'),
            'total_processing_time_seconds': round(total_time, 3),
            'model_file': config.MODEL_FILENAME,
            'api_version': config.API_VERSION
        })
        
    except Exception as e:
        logger.error(f"Batch classification error: {e}")
        return jsonify({'status': 'error', 'error': str(e)}), 500


# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': str(e.description)
    }), 429


@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal error: {error}")
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500


# ============================================================================
# STARTUP
# ============================================================================

if __name__ == '__main__':
    print("="*60)
    print("SOLAR CLASSIFIER API - PRODUCTION")
    print("="*60)
    print(f"Environment: {os.environ.get('FLASK_ENV', 'production')}")
    print(f"Model file: {config.MODEL_FILENAME}")
    print(f"Model path: {config.MODEL_PATH}")
    print("="*60)
    
    if load_model():
        print("✅ Model loaded successfully!")
        print(f"🌐 Starting API server...")
        print(f"📍 API endpoints available at http://localhost:8080")
        print("="*60)
        port = int(os.environ.get("PORT", 8080))
        # Note: In production, use Gunicorn instead of Flask's built-in server
        app.run(host='0.0.0.0', port=port, debug=config.DEBUG)
    else:
        print("❌ Failed to load model. Please check the logs.")
        print(f"📁 Ensure model file '{config.MODEL_FILENAME}' exists in 'models/' directory")