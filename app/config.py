"""
Configuration management for Solar Classifier API
"""
import os
from pathlib import Path
from dotenv import load_dotenv
import logging

# Configure logging for this module
logger = logging.getLogger(__name__)

# Load environment variables with better error handling
def load_environment_variables():
    """Load environment variables from .env file with fallback handling"""
    env_file = Path(__file__).resolve().parent.parent / '.env'

    if env_file.exists():
        logger.info(f"Loading environment variables from {env_file}")
        load_dotenv(env_file)
        return True
    else:
        logger.warning(f".env file not found at {env_file}")
        logger.info("Using system environment variables only")
        return False

# Load environment variables
from dotenv import find_dotenv
env_path = find_dotenv()
load_result = load_dotenv(env_path)

# Store debug info about env loading
ENV_LOAD_DEBUG = {
    'env_path': str(env_path) if env_path else None,
    'load_result': load_result,
    'working_dir': os.getcwd(),
    'env_file_in_cwd': os.path.exists('.env'),
    'env_path_exists': os.path.exists(env_path) if env_path else False,
    'base_dir': str(Path(__file__).resolve().parent.parent)
}



class Config:
    """Base configuration"""
    # Paths
    BASE_DIR = Path(__file__).resolve().parent.parent
    MODEL_DIR = BASE_DIR / 'models'
    CACHE_DIR = BASE_DIR / 'cache'
    LOG_DIR = BASE_DIR / 'logs'
    
    # API Settings
    API_VERSION = "1.0.0"
    API_TITLE = "Solar Classifier API"
    API_DESCRIPTION = "AI-powered solar roof classification using Google Maps imagery"
    
    # Model Settings
    MODEL_FILENAME = "google_classifier_robust_best.pth"  # Hardcoded for remote deployment
    MODEL_PATH = MODEL_DIR / MODEL_FILENAME
    MODEL_DEVICE = os.environ.get('MODEL_DEVICE', 'cpu')
    NUM_CLASSES = 4  # google_classifier_robust_best.pth has 4 classes
    
    # Google Maps Settings
    GOOGLE_API_KEY = "AIzaSyBhEdAEkurpb6qiaPSCtACJnGAke6h1Ahc"
    GOOGLE_MAPS_BASE_URL = "https://maps.googleapis.com/maps/api/staticmap"

    IMAGE_SIZE = "640x640"
    ZOOM_LEVEL = 20
    MAP_TYPE = "satellite"
    
    # Security
    # API_KEY = os.environ.get('API_KEY', 'X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4')
    API_KEY = "X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4"
    RATE_LIMIT_PER_MINUTE = int(os.environ.get('RATE_LIMIT_PER_MINUTE', '60'))
    RATE_LIMIT_PER_HOUR = int(os.environ.get('RATE_LIMIT_PER_HOUR', '1000'))
    
    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Cache Settings
    MAX_CACHE_SIZE_MB = int(os.environ.get('MAX_CACHE_SIZE_MB', '10000'))  # 10GB
    CACHE_CLEANUP_DAYS = int(os.environ.get('CACHE_CLEANUP_DAYS', '30'))
    
    # Performance
    REQUEST_TIMEOUT = int(os.environ.get('REQUEST_TIMEOUT', '30'))
    MAX_BATCH_SIZE = int(os.environ.get('MAX_BATCH_SIZE', '50'))
    
    # Monitoring
    ENABLE_METRICS = os.environ.get('ENABLE_METRICS', 'true').lower() == 'true'
    METRICS_PORT = int(os.environ.get('METRICS_PORT', '9090'))


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Override with production values
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'WARNING')
    

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    API_KEY = 'test-key'


# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': ProductionConfig
}


def get_config():
    """Get configuration based on environment"""
    env = os.environ.get('FLASK_ENV', 'production')
    return config.get(env, config['default'])