"""
Model definitions for Solar Classifier
"""
import torch
import torch.nn as nn
from torchvision import models


class SolarClassificationModel(nn.Module):
    """
    Multi-task neural network for solar roof classification
    
    Tasks:
    1. Detailed classification: 5-class (good, shade, solar, mobile_home, no_home)
    2. Binary classification: pass/fail for solar suitability
    """
    
    def __init__(self, num_detailed_classes=5, dropout_rate=0.3):
        super(SolarClassificationModel, self).__init__()
        
        # Load pretrained EfficientNet-B0 backbone
        self.backbone = models.efficientnet_b0(weights='IMAGENET1K_V1')
        
        # Get feature dimensions
        self.feature_dim = self.backbone.classifier[1].in_features
        
        # Remove original classifier
        self.backbone.classifier = nn.Identity()
        
        # Add dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # Detailed classification head (5 classes)
        self.detailed_classifier = nn.Sequential(
            nn.Linear(self.feature_dim, 512),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(512, num_detailed_classes)
        )
        
        # Binary classification head
        self.binary_classifier = nn.Sequential(
            nn.Linear(self.feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 1)
        )
        
        # Class names
        if num_detailed_classes == 4:
            self.detailed_classes = ['good', 'shade', 'solar', 'mobile_home']
        else:
            self.detailed_classes = ['good', 'shade', 'solar', 'mobile_home', 'no_house']
        self.binary_classes = ['fail', 'pass']
    
    def forward(self, x):
        # Extract features
        features = self.backbone(x)
        features = self.dropout(features)
        
        # Generate predictions
        detailed_pred = self.detailed_classifier(features)
        binary_pred = self.binary_classifier(features)
        
        return detailed_pred, binary_pred