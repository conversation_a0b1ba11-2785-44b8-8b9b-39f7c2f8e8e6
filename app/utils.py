"""
Utility functions for Solar Classifier API
"""
import os
import time
import hashlib
import requests
import numpy as np
from PIL import Image
from io import BytesIO
from pathlib import Path
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class GoogleMapsImageExtractor:
    """
    Extract aerial images using Google Maps Static API with caching
    """
    
    def __init__(self, api_key, cache_dir, config):
        self.api_key = api_key
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Google Maps settings from config
        self.base_url = config.GOOGLE_MAPS_BASE_URL
        self.image_size = config.IMAGE_SIZE
        self.zoom_level = config.ZOOM_LEVEL
        self.map_type = config.MAP_TYPE
        self.image_format = "jpg"
        
        # Statistics
        self.stats = {
            'downloads': 0,
            'cache_hits': 0,
            'failures': 0,
            'total_requests': 0
        }
        
        # Pre-load cache list for performance
        self._refresh_cache_list()
        logger.info(f"Google Maps extractor initialized with {len(self.cached_files)} cached images")
    
    def _refresh_cache_list(self):
        """Pre-load list of cached files"""
        self.cached_files = set(f.name for f in self.cache_dir.glob('*.jpg'))
    
    def extract_image(self, latitude, longitude):
        """Extract aerial image for given coordinates"""
        self.stats['total_requests'] += 1
        
        # Generate cache filename
        filename = f"google_{latitude:.6f}_{longitude:.6f}.jpg"
        cache_path = self.cache_dir / filename
        
        # Check cache first
        if filename in self.cached_files:
            self.stats['cache_hits'] += 1
            return self._load_cached_image(cache_path)
        
        # Download from Google Maps
        self.stats['downloads'] += 1
        
        try:
            params = {
                'center': f"{latitude},{longitude}",
                'zoom': self.zoom_level,
                'size': self.image_size,
                'maptype': self.map_type,
                'format': self.image_format,
                'key': self.api_key
            }
            
            response = requests.get(self.base_url, params=params, timeout=15)
            
            if response.status_code != 200:
                error_body = response.text if response.text else "No error details"
                logger.error(f"Google Maps API error {response.status_code}: {error_body}")
                raise Exception(f"Google Maps API returned {response.status_code}: {error_body}")
            
            response.raise_for_status()
            
            # Load and process image
            image = Image.open(BytesIO(response.content))
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            image_data = np.array(image)
            
            # Cache the image
            self._cache_image(image, cache_path)
            self.cached_files.add(filename)
            
            return image_data
            
        except Exception as e:
            self.stats['failures'] += 1
            logger.error(f"Failed to download image for ({latitude:.6f}, {longitude:.6f}): {e}")
            return None
    
    def _load_cached_image(self, cache_path):
        """Load image from cache"""
        try:
            image = Image.open(cache_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return np.array(image)
        except Exception as e:
            logger.error(f"Cache read error: {e}")
            self.cached_files.discard(cache_path.name)
            return None
    
    def _cache_image(self, image, cache_path):
        """Save image to cache"""
        try:
            image.save(cache_path, 'JPEG', quality=95)
        except Exception as e:
            logger.error(f"Cache write error: {e}")
    
    def get_stats(self):
        """Get extractor statistics"""
        return self.stats.copy()
    
    def cleanup_old_cache(self, days=30):
        """Remove cache files older than specified days"""
        cutoff_time = datetime.now() - timedelta(days=days)
        removed_count = 0
        
        for cache_file in self.cache_dir.glob('*.jpg'):
            if datetime.fromtimestamp(cache_file.stat().st_mtime) < cutoff_time:
                try:
                    cache_file.unlink()
                    removed_count += 1
                except Exception as e:
                    logger.error(f"Failed to remove cache file {cache_file}: {e}")
        
        if removed_count > 0:
            self._refresh_cache_list()
            logger.info(f"Removed {removed_count} old cache files")
        
        return removed_count


def verify_api_key(request, config):
    """
    Verify API key from request
    Returns: (is_valid, error_message)
    """
    api_key = request.headers.get('X-API-Key') or request.json.get('api_key', '') if request.json else ''
    
    if not api_key:
        return False, "Missing API key! This is the correct key: " + config.API_KEY
    
    if api_key != config.API_KEY:
        return False, "Invalid API key! This is the correct key: " + config.API_KEY
    
    return True, None


def format_classification_response(result, coordinates, processing_time, config):
    """Format classification result for API response"""
    return {
        'status': 'success',
        'coordinates': coordinates,
        'classification': {
            'detailed_class': result['detailed_class'],
            'detailed_confidence': round(result['detailed_confidence'] * 100, 1),
            'binary_class': result['binary_class'],
            'solar_suitable': result['solar_suitable'],
            'suitability_probability': round(result['binary_probability'] * 100, 1)
        },
        'probabilities': {
            k: round(v * 100, 1) for k, v in result['all_probabilities'].items()
        },
        'processing_time_seconds': round(processing_time, 3),
        'api_version': config.API_VERSION,
        'model_file': config.MODEL_FILENAME,
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    }


def validate_coordinates(latitude, longitude):
    """
    Validate coordinate values
    Returns: (is_valid, error_message)
    """
    try:
        lat = float(latitude)
        lon = float(longitude)
    except (ValueError, TypeError):
        return False, "Invalid coordinate format"
    
    if not (-90 <= lat <= 90):
        return False, f"Latitude {lat} out of range (-90 to 90)"
    
    if not (-180 <= lon <= 180):
        return False, f"Longitude {lon} out of range (-180 to 180)"
    
    return True, None