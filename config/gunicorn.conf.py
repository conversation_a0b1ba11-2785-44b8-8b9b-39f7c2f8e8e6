"""
Gunicorn configuration for production deployment
"""
import multiprocessing
import os

# Server socket
bind = "0.0.0.0:8080"
backlog = 2048

# Worker processes - optimized for Cloud Run HTTP/2
workers = 1  # Single worker for Cloud Run
worker_class = "gthread"  # Better HTTP/2 support than gevent
threads = 8  # Increased threads for concurrency
worker_connections = 1000
timeout = 60  # Reduced timeout for better responsiveness
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'solar-classifier-api'

# Server mechanics
daemon = False
pidfile = 'logs/gunicorn.pid'
user = None
group = None
tmp_upload_dir = None

# SSL (uncomment for HTTPS)
# keyfile = 'path/to/keyfile'
# certfile = 'path/to/certfile'

# Pre-load application
preload_app = True

# Enable automatic worker restarts
reload = os.environ.get('FLASK_ENV') == 'development'

# HTTP/2 specific settings for Cloud Run
# Disable sendfile for better HTTP/2 compatibility
sendfile = False

# Enable proper connection handling
reuse_port = True

# Graceful timeout for HTTP/2 connections
graceful_timeout = 30