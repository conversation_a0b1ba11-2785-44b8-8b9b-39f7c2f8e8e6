# Nginx configuration for Solar Classifier API
# Place this in /etc/nginx/sites-available/solar-api

upstream solar_api {
    server 127.0.0.1:8080;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=classify_limit:10m rate=5r/s;

server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain

    # Redirect HTTP to HTTPS (uncomment when SSL is configured)
    # return 301 https://$server_name$request_uri;
    
    # For initial setup without SSL, comment out the return above and uncomment below:
    location / {
        proxy_pass http://solar_api;
        include /etc/nginx/proxy_params;
    }
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # Replace with your domain

    # SSL configuration (update paths)
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # SSL security settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Logging
    access_log /var/log/nginx/solar-api-access.log;
    error_log /var/log/nginx/solar-api-error.log;

    # Max upload size (for batch requests)
    client_max_body_size 10M;

    # Timeouts
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;

    # API endpoints
    location / {
        # Apply general rate limiting
        limit_req zone=api_limit burst=20 nodelay;
        
        proxy_pass http://solar_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Stricter rate limiting for classification endpoints
    location ~ ^/(classify|classify-batch)$ {
        limit_req zone=classify_limit burst=10 nodelay;
        
        proxy_pass http://solar_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        proxy_pass http://solar_api;
        proxy_set_header Host $host;
        access_log off;  # Don't log health checks
    }

    # Static files (if any)
    location /static {
        alias /home/<USER>/prod_repo_solar_classifier/static;
        expires 1d;
    }
}