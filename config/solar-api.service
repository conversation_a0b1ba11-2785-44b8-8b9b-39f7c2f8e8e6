[Unit]
Description=Solar Classifier API
After=network.target

[Service]
Type=notify
User=solarapi
Group=solarapi
WorkingDirectory=/home/<USER>/prod_repo_solar_classifier
Environment="PATH=/home/<USER>/prod_repo_solar_classifier/venv/bin"
Environment="FLASK_ENV=production"
ExecStart=/home/<USER>/prod_repo_solar_classifier/venv/bin/gunicorn \
    --config /home/<USER>/prod_repo_solar_classifier/config/gunicorn.conf.py \
    wsgi:application

# Restart policy
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true

# Resource limits
LimitNOFILE=65535
# Memory limit (adjust based on your server)
MemoryLimit=2G

# Logging
StandardOutput=append:/home/<USER>/prod_repo_solar_classifier/logs/solar-api.log
StandardError=append:/home/<USER>/prod_repo_solar_classifier/logs/solar-api-error.log

[Install]
WantedBy=multi-user.target