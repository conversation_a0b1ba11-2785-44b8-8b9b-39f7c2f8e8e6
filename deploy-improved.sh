#!/bin/bash

# Improved Flask App Deployment Script for Google Cloud Run
# Usage: ./deploy-improved.sh [dev|prod]

set -e  # Exit on any error

# Check if environment argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [dev|prod]"
    echo "  dev  - Deploy to qa-sr-development project"
    echo "  prod - Deploy to qa-sr-production project"
    exit 1
fi

ENVIRONMENT=$1
PROJECT_ID="admin-solarreviews"

# Set project ID based on environment
case $ENVIRONMENT in
    dev)
        SERVICE_NAME="qa-sr-development"
        ;;
    prod)
        SERVICE_NAME="qa-sr-production"
        ;;
    *)
        echo "Error: Environment must be 'dev' or 'prod'"
        echo "Usage: $0 [dev|prod]"
        exit 1
        ;;
esac

echo "Deploying to $ENVIRONMENT environment..."
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"

# Set the Google Cloud project
echo "Setting Google Cloud project..."
gcloud config set project $PROJECT_ID

# Enable required APIs (if not already enabled)
echo "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com run.googleapis.com --quiet

# Generate environment variables from .env file
echo "Generating environment variables from .env file..."
source scripts/generate-env-vars.sh $ENVIRONMENT

# Set environment-specific configurations
case $ENVIRONMENT in
    dev)
        MEMORY="2Gi"
        MAX_INSTANCES="10"
        ;;
    prod)
        MEMORY="4Gi"
        MAX_INSTANCES="100"
        ;;
esac

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory $MEMORY \
    --max-instances $MAX_INSTANCES \
    --set-env-vars "$GENERATED_ENV_VARS" \
    --quiet

echo "Deployment complete."
echo "Your app is available at:"
gcloud run services describe $SERVICE_NAME --region=us-central1 --format="value(status.url)"
