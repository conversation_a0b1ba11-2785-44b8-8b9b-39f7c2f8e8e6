#!/bin/bash

# Flask App Deployment Script for Google Cloud Run
# Usage: ./deploy.sh [dev|prod]

set -e  # Exit on any error

# Check if environment argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [dev|prod]"
    echo "  dev  - Deploy to qa-sr-development project"
    echo "  prod - Deploy to qa-sr-production project"
    exit 1
fi

ENVIRONMENT=$1
PROJECT_ID="admin-solarreviews"

# Set project ID based on environment
case $ENVIRONMENT in
    dev)
        SERVICE_NAME="qa-sr-development"
        ;;
    prod)
        SERVICE_NAME="qa-sr-production"
        ;;
    *)
        echo "Error: Environment must be 'dev' or 'prod'"
        echo "Usage: $0 [dev|prod]"
        exit 1
        ;;
esac

echo "Deploying to $ENVIRONMENT environment..."
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"

# Set the Google Cloud project
echo "Setting Google Cloud project..."
gcloud config set project $PROJECT_ID

# Enable required APIs (if not already enabled)
echo "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com run.googleapis.com --quiet

# Set environment-specific configurations
case $ENVIRONMENT in
    dev)
        ENV_VARS="FLASK_ENV=development,DEBUG=True,MODEL_FILENAME=google_classifier_robust_best.pth,MODEL_DEVICE=cpu,NUM_CLASSES=5,API_KEY=X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4,GOOGLE_MAPS_API_KEY=AIzaSyBhEdAEkurpb6qiaPSCtACJnGAke6h1Ahc,RATE_LIMIT_PER_MINUTE=60,RATE_LIMIT_PER_HOUR=1000,LOG_LEVEL=INFO,MAX_CACHE_SIZE_MB=10000,CACHE_CLEANUP_DAYS=30,REQUEST_TIMEOUT=30,MAX_BATCH_SIZE=50,ENABLE_METRICS=true,METRICS_PORT=9090"
        MEMORY="2Gi"
        MAX_INSTANCES="10"
        ;;
    prod)
        ENV_VARS="FLASK_ENV=production,DEBUG=False,MODEL_FILENAME=google_classifier_robust_best.pth,MODEL_DEVICE=cpu,NUM_CLASSES=5,API_KEY=X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4,GOOGLE_MAPS_API_KEY=AIzaSyBhEdAEkurpb6qiaPSCtACJnGAke6h1Ahc,RATE_LIMIT_PER_MINUTE=60,RATE_LIMIT_PER_HOUR=1000,LOG_LEVEL=WARNING,MAX_CACHE_SIZE_MB=10000,CACHE_CLEANUP_DAYS=30,REQUEST_TIMEOUT=30,MAX_BATCH_SIZE=50,ENABLE_METRICS=true,METRICS_PORT=9090"
        MEMORY="4Gi"
        MAX_INSTANCES="100"
        ;;
esac

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory $MEMORY \
    --max-instances $MAX_INSTANCES \
    --set-env-vars $ENV_VARS \
    --quiet

echo "Deployment complete."
echo "Your app is available at:"
gcloud run services describe $SERVICE_NAME --region=us-central1 --format="value(status.url)"