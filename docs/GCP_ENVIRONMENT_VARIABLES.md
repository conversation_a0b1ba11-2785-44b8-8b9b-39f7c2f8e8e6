# GCP Environment Variables Fix

## Problem
Environment variables from the `.env` file were not being loaded in the GCP Cloud Run environment, causing the application to use default values instead of the configured ones.

## Root Cause
1. **Container Environment**: In GCP Cloud Run, the `.env` file is copied to the container but not automatically loaded
2. **Deployment Scripts**: The deployment scripts (`deploy.sh`, `manual-deploy.sh`) were only setting basic environment variables (`FLASK_ENV`, `DEBUG`) via `--set-env-vars`
3. **Missing Variables**: Critical variables like `MODEL_FILENAME`, `API_KEY`, `GOOGLE_MAPS_API_KEY`, etc. were not being passed to the Cloud Run service
4. **Reserved Variables**: Cloud Run automatically sets certain environment variables like `PORT`, and attempting to override them causes deployment failures

## Solutions Implemented

### Solution 1: Updated Deployment Scripts (Quick Fix)
Updated `deploy.sh` and `manual-deploy.sh` to include all environment variables from `.env` file:

```bash
# Before (only basic vars)
--set-env-vars FLASK_ENV=production,DEBUG=False

# After (all vars)
--set-env-vars FLASK_ENV=production,DEBUG=False,MODEL_FILENAME=google_classifier_robust_best.pth,MODEL_DEVICE=cpu,NUM_CLASSES=5,API_KEY=X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4,GOOGLE_MAPS_API_KEY=AIzaSyBhEdAEkurpb6qiaPSCtACJnGAke6h1Ahc,RATE_LIMIT_PER_MINUTE=60,RATE_LIMIT_PER_HOUR=1000,LOG_LEVEL=WARNING,MAX_CACHE_SIZE_MB=10000,CACHE_CLEANUP_DAYS=30,REQUEST_TIMEOUT=30,MAX_BATCH_SIZE=50,ENABLE_METRICS=true,METRICS_PORT=9090
```

### Solution 2: Automated Environment Variable Generation (Recommended)
Created `scripts/generate-env-vars.sh` that automatically reads the `.env` file and generates the environment variables string for deployment:

**Features:**
- Automatically excludes reserved Cloud Run variables (`PORT`, `HOST`, `WORKERS`, etc.)
- Supports environment-specific overrides
- Handles quoted values and comments properly

**Usage:**
```bash
# Generate environment variables for production
source scripts/generate-env-vars.sh prod

# Use in deployment
gcloud run deploy $SERVICE_NAME \
    --set-env-vars "$GENERATED_ENV_VARS" \
    ...
```

**New Deployment Script:**
Use `deploy-improved.sh` which automatically loads all environment variables from `.env`:

```bash
./deploy-improved.sh prod
```

### Solution 3: Improved Configuration Loading
Enhanced `app/config.py` to provide better logging and error handling for environment variable loading:

- Added logging to show when `.env` file is found/not found
- Better error handling for missing environment variables
- More robust path resolution for containerized environments

## Testing
Created `scripts/test-env-vars.py` to verify environment variables are loaded correctly:

```bash
# Test locally
source venv/bin/activate
python3 scripts/test-env-vars.py
```

## Deployment Options

### Option A: Quick Fix (Immediate Solution)
```bash
./quick-deploy-fix.sh dev
# or
./quick-deploy-fix.sh prod
```

### Option B: Use Improved Deployment Script (Recommended)
```bash
./deploy-improved.sh prod
```

### Option C: Use Updated Original Scripts
```bash
./deploy.sh prod
# or
./manual-deploy.sh prod
```

### Option C: Manual Deployment with Generated Variables
```bash
source scripts/generate-env-vars.sh prod
gcloud run deploy qa-sr-production \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 4Gi \
    --max-instances 100 \
    --set-env-vars "$GENERATED_ENV_VARS" \
    --quiet
```

## Verification
After deployment, you can verify environment variables are set correctly by:

1. **Check Cloud Run Console**: Go to the service in GCP Console → Variables tab
2. **Check Application Logs**: Look for configuration loading messages
3. **Test API Endpoints**: Verify the application is using correct model file, API keys, etc.

## Files Modified/Created
- ✅ `deploy.sh` - Updated with all environment variables
- ✅ `manual-deploy.sh` - Updated with all environment variables  
- ✅ `scripts/generate-env-vars.sh` - New automated env var generator
- ✅ `deploy-improved.sh` - New deployment script using automated generation
- ✅ `app/config.py` - Enhanced with better logging and error handling
- ✅ `scripts/test-env-vars.py` - New testing script
- ✅ `docs/GCP_ENVIRONMENT_VARIABLES.md` - This documentation

## Best Practices Going Forward
1. **Use the improved deployment script** (`deploy-improved.sh`) for new deployments
2. **Test environment variables locally** before deploying using `scripts/test-env-vars.py`
3. **Keep `.env` file updated** with all required variables
4. **Use environment-specific overrides** in deployment scripts for different environments
5. **Monitor application logs** after deployment to ensure proper configuration loading
