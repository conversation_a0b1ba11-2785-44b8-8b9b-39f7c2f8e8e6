# Terraform Setup and Best Practices

## Issue: Large Files in Git
Terraform provider binaries and state files should **never** be committed to Git because:
- Provider binaries are large (100MB+) and exceed GitHub's file size limits
- State files contain sensitive information (API keys, resource IDs)
- These files are environment-specific and regenerated automatically

## Quick Fix

### Step 1: Clean Up Git Repository
```bash
# Run the cleanup script
./scripts/cleanup-terraform-git.sh
```

### Step 2: Push Changes
```bash
git push origin main
```

## Terraform Best Practices

### Files to NEVER Commit
- `.terraform/` - Contains provider binaries (large files)
- `terraform.tfstate` - Contains sensitive state data
- `terraform.tfstate.backup` - Backup of state file
- `*.tfvars` - May contain sensitive variables

### Files to Commit
- `*.tf` - Terraform configuration files
- `*.tf.example` - Example variable files
- `.terraform.lock.hcl` - Provider version locks (optional, but recommended)

### Updated .gitignore
Your `.gitignore` now includes:
```
# Terraform
.terraform/
.terraform.lock.hcl
terraform.tfstate
terraform.tfstate.backup
*.tfplan
*.tfvars
```

## Remote State Management (Recommended)

Instead of local state files, use remote state storage:

### Option 1: Google Cloud Storage
```hcl
terraform {
  backend "gcs" {
    bucket = "your-terraform-state-bucket"
    prefix = "solar-classifier/state"
  }
}
```

### Option 2: Terraform Cloud
```hcl
terraform {
  cloud {
    organization = "your-org"
    workspaces {
      name = "solar-classifier"
    }
  }
}
```

## Local Development Setup

### Initial Setup
```bash
# Initialize Terraform (downloads providers)
terraform init

# Plan changes
terraform plan

# Apply changes
terraform apply
```

### After Git Clone
```bash
# Reinitialize Terraform
terraform init

# The .terraform/ directory will be recreated automatically
```

## Security Notes

1. **Never commit state files** - They contain sensitive resource information
2. **Use remote state** - Store state in secure, encrypted backends
3. **Use variable files carefully** - Don't commit files with sensitive values
4. **Review .gitignore** - Ensure all sensitive files are excluded

## Troubleshooting

### "File too large" Error
If you get GitHub file size errors:
1. Run `./scripts/cleanup-terraform-git.sh`
2. Commit the changes
3. Push to GitHub

### Missing .terraform Directory
This is normal after cloning. Run:
```bash
terraform init
```

### State File Issues
If you have state file conflicts:
1. Never commit state files to Git
2. Use remote state storage
3. Coordinate with team members on state changes

## Current Infrastructure

Your `main.tf` defines:
- Google Compute Network and Subnetwork
- Compute Engine instance (qa-sr1)
- Firewall rules for SSH and Flask app
- External IP configuration

This infrastructure is separate from your Cloud Run deployment and can be managed independently.
