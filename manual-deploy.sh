#!/bin/bash

# Manual Deployment Script for Google Cloud Run
# Usage: ./manual-deploy.sh [dev|prod] [image-tag]

set -e  # Exit on any error

# Check if environment argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [dev|prod] [image-tag]"
    echo "  dev       - Deploy to qa-sr-development project"
    echo "  prod      - Deploy to qa-sr-production project"
    echo "  image-tag - Specific image tag to deploy (optional, defaults to 'latest')"
    echo ""
    echo "Examples:"
    echo "  $0 dev                    - Deploy latest image to development"
    echo "  $0 prod                   - Deploy latest image to production"
    echo "  $0 dev abc123             - Deploy image with tag 'abc123' to development"
    exit 1
fi

ENVIRONMENT=$1
IMAGE_TAG=${2:-latest}
PROJECT_ID="admin-solarreviews"

# Set project ID based on environment
case $ENVIRONMENT in
    dev)
        SERVICE_NAME="qa-sr-development"
        FLASK_ENV="development"
        DEBUG="True"
        MEMORY="2Gi"
        MAX_INSTANCES="10"
        ;;
    prod)
        SERVICE_NAME="qa-sr-production"
        FLASK_ENV="production"
        DEBUG="False"
        MEMORY="4Gi"
        MAX_INSTANCES="100"
        ;;
    *)
        echo "Error: Environment must be 'dev' or 'prod'"
        echo "Usage: $0 [dev|prod] [image-tag]"
        exit 1
        ;;
esac

echo "Manual deployment to $ENVIRONMENT environment..."
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Image: gcr.io/$PROJECT_ID/$SERVICE_NAME:$IMAGE_TAG"

# Set the Google Cloud project
echo "Setting Google Cloud project..."
gcloud config set project $PROJECT_ID

# Check if image exists
echo "Checking if image exists..."
if ! gcloud container images describe "gcr.io/$PROJECT_ID/$SERVICE_NAME:$IMAGE_TAG" >/dev/null 2>&1; then
    echo "Error: Image gcr.io/$PROJECT_ID/$SERVICE_NAME:$IMAGE_TAG not found"
    echo "Available images:"
    gcloud container images list-tags "gcr.io/$PROJECT_ID/$SERVICE_NAME" --limit=10
    exit 1
fi

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image "gcr.io/$PROJECT_ID/$SERVICE_NAME:$IMAGE_TAG" \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory $MEMORY \
    --max-instances $MAX_INSTANCES \
    --set-env-vars FLASK_ENV=$FLASK_ENV,DEBUG=$DEBUG,MODEL_FILENAME=google_classifier_robust_best.pth,MODEL_DEVICE=cpu,NUM_CLASSES=5,API_KEY=X7K9mN2pQ8rT5vB3hF6gD4sL1wZ0aE9jU7yR2qW5xM4,GOOGLE_MAPS_API_KEY=AIzaSyBhEdAEkurpb6qiaPSCtACJnGAke6h1Ahc,RATE_LIMIT_PER_MINUTE=60,RATE_LIMIT_PER_HOUR=1000,LOG_LEVEL=INFO,MAX_CACHE_SIZE_MB=10000,CACHE_CLEANUP_DAYS=30,REQUEST_TIMEOUT=30,MAX_BATCH_SIZE=50,ENABLE_METRICS=true,METRICS_PORT=9090 \
    --quiet

echo "Manual deployment complete."
echo "Your app is available at:"
gcloud run services describe $SERVICE_NAME --region=us-central1 --format="value(status.url)" 