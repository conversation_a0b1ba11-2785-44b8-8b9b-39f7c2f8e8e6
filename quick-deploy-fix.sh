#!/bin/bash

# Quick deployment fix for the PORT variable issue
# Usage: ./quick-deploy-fix.sh [dev|prod]

set -e

if [ $# -eq 0 ]; then
    echo "Usage: $0 [dev|prod]"
    exit 1
fi

ENVIRONMENT=$1
PROJECT_ID="admin-solarreviews"

case $ENVIRONMENT in
    dev)
        SERVICE_NAME="qa-sr-development"
        MEMORY="2Gi"
        MAX_INSTANCES="10"
        ;;
    prod)
        SERVICE_NAME="qa-sr-production"
        MEMORY="4Gi"
        MAX_INSTANCES="100"
        ;;
    *)
        echo "Error: Environment must be 'dev' or 'prod'"
        exit 1
        ;;
esac

echo "Quick deployment fix for $ENVIRONMENT environment..."
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"

# Set the Google Cloud project
gcloud config set project $PROJECT_ID

# Generate environment variables (excluding reserved ones)
source scripts/generate-env-vars.sh $ENVIRONMENT

echo "Deploying to Cloud Run with fixed environment variables..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory $MEMORY \
    --max-instances $MAX_INSTANCES \
    --set-env-vars "$GENERATED_ENV_VARS" \
    --quiet

echo "Deployment complete!"
echo "Your app is available at:"
gcloud run services describe $SERVICE_NAME --region=us-central1 --format="value(status.url)"
