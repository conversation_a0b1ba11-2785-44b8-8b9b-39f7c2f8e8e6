#!/bin/bash

# <PERSON>ript to clean up Terraform files from Git repository
# This removes large Terraform provider files that shouldn't be in version control

set -e

echo "🧹 Cleaning up Terraform files from Git repository..."
echo "⚠️  WARNING: This will modify Git history. Make sure you have a backup!"
echo ""

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Error: Not in a Git repository"
    exit 1
fi

# Check for uncommitted changes
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  You have uncommitted changes. Please commit or stash them first."
    echo "Uncommitted files:"
    git status --porcelain
    exit 1
fi

echo "📋 Current repository status:"
echo "Branch: $(git branch --show-current)"
echo "Last commit: $(git log -1 --oneline)"
echo ""

# Show what Terraform files are currently tracked
echo "🔍 Terraform files currently tracked by Git:"
git ls-files | grep -E '\.(terraform|tfstate|tfplan|tfvars)$|\.terraform/' || echo "None found"
echo ""

# Ask for confirmation
read -p "Do you want to proceed with cleaning up Terraform files? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Aborted"
    exit 1
fi

echo "🗑️  Removing Terraform files from Git history..."

# Remove .terraform directory and related files from Git
git rm -r --cached .terraform/ 2>/dev/null || echo "No .terraform/ directory in Git"
git rm --cached terraform.tfstate 2>/dev/null || echo "No terraform.tfstate in Git"
git rm --cached terraform.tfstate.backup 2>/dev/null || echo "No terraform.tfstate.backup in Git"
git rm --cached .terraform.lock.hcl 2>/dev/null || echo "No .terraform.lock.hcl in Git"
git rm --cached *.tfplan 2>/dev/null || echo "No .tfplan files in Git"
git rm --cached *.tfvars 2>/dev/null || echo "No .tfvars files in Git"

# Check if there are any changes to commit
if git diff-index --quiet --cached HEAD --; then
    echo "✅ No Terraform files were tracked by Git"
else
    echo "💾 Committing removal of Terraform files..."
    git commit -m "Remove Terraform files from version control

- Remove .terraform/ directory with large provider binaries
- Remove terraform.tfstate files (contain sensitive data)
- These files should not be in version control
- Updated .gitignore to prevent future commits"
fi

echo ""
echo "✅ Cleanup complete!"
echo ""
echo "📝 Next steps:"
echo "1. The large Terraform files have been removed from Git"
echo "2. Your .gitignore has been updated to prevent future commits"
echo "3. You can now push to GitHub without file size issues"
echo ""
echo "🔧 To reinitialize Terraform (if needed):"
echo "   terraform init"
echo ""
echo "⚠️  Important notes:"
echo "- terraform.tfstate contains sensitive data and should never be in Git"
echo "- Use Terraform Cloud, AWS S3, or GCS for remote state storage"
echo "- The .terraform/ directory will be recreated when you run 'terraform init'"
