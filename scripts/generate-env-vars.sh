#!/bin/bash

# Script to generate environment variables from .env file for GCP deployment
# Usage: source scripts/generate-env-vars.sh [environment]

set -e

ENVIRONMENT=${1:-production}
ENV_FILE=".env"

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env file not found!"
    exit 1
fi

echo "Generating environment variables from $ENV_FILE for $ENVIRONMENT environment..."

# List of reserved environment variables that Cloud Run sets automatically
# or variables that shouldn't be set via environment variables
RESERVED_VARS=("PORT" "HOST" "WORKERS" "K_SERVICE" "K_REVISION" "K_CONFIGURATION")

# Read .env file and convert to comma-separated format for gcloud
ENV_VARS=""

while IFS='=' read -r key value || [ -n "$key" ]; do
    # Skip empty lines and comments
    if [[ -z "$key" || "$key" =~ ^[[:space:]]*# ]]; then
        continue
    fi

    # Remove leading/trailing whitespace
    key=$(echo "$key" | xargs)
    value=$(echo "$value" | xargs)

    # Skip if key is empty after trimming
    if [[ -z "$key" ]]; then
        continue
    fi

    # Skip reserved environment variables
    skip_var=false
    for reserved in "${RESERVED_VARS[@]}"; do
        if [[ "$key" == "$reserved" ]]; then
            echo "Skipping reserved environment variable: $key"
            skip_var=true
            break
        fi
    done

    if [[ "$skip_var" == true ]]; then
        continue
    fi

    # Remove quotes if present
    value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')

    # Add to ENV_VARS string
    if [ -z "$ENV_VARS" ]; then
        ENV_VARS="${key}=${value}"
    else
        ENV_VARS="${ENV_VARS},${key}=${value}"
    fi
done < "$ENV_FILE"

# Override specific values based on environment
case $ENVIRONMENT in
    development|dev)
        ENV_VARS="${ENV_VARS},FLASK_ENV=development,DEBUG=True,LOG_LEVEL=DEBUG"
        ;;
    production|prod)
        ENV_VARS="${ENV_VARS},FLASK_ENV=production,DEBUG=False,LOG_LEVEL=WARNING"
        ;;
esac

# Export for use in other scripts
export GENERATED_ENV_VARS="$ENV_VARS"

echo "Environment variables generated successfully!"
echo "Use \$GENERATED_ENV_VARS in your deployment script"
