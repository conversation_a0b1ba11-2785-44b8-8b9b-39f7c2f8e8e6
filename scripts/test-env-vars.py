#!/usr/bin/env python3
"""
Test script to verify environment variables are loaded correctly
"""
import os
import sys
from pathlib import Path

# Add the project directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import get_config

def test_environment_variables():
    """Test that all required environment variables are available"""
    
    print("="*60)
    print("ENVIRONMENT VARIABLES TEST")
    print("="*60)
    
    # Get configuration
    config = get_config()()
    
    # List of critical environment variables
    critical_vars = [
        'MODEL_FILENAME',
        'MODEL_DEVICE', 
        'API_KEY',
        'GOOGLE_MAPS_API_KEY',
        'FLASK_ENV'
    ]
    
    # List of optional environment variables
    optional_vars = [
        'RATE_LIMIT_PER_MINUTE',
        'RATE_LIMIT_PER_HOUR',
        'LOG_LEVEL',
        'MAX_CACHE_SIZE_MB',
        'CACHE_CLEANUP_DAYS',
        'REQUEST_TIMEOUT',
        'MAX_BATCH_SIZE',
        'ENABLE_METRICS',
        'METRICS_PORT'
    ]
    
    print(f"Environment: {os.environ.get('FLASK_ENV', 'NOT SET')}")
    print(f"Configuration class: {config.__class__.__name__}")
    print()
    
    # Check critical variables
    print("CRITICAL ENVIRONMENT VARIABLES:")
    print("-" * 40)
    missing_critical = []
    
    for var in critical_vars:
        value = os.environ.get(var)
        config_value = getattr(config, var, 'NOT SET IN CONFIG')
        
        if value:
            print(f"✅ {var}: {value[:20]}{'...' if len(str(value)) > 20 else ''}")
        else:
            print(f"❌ {var}: NOT SET")
            missing_critical.append(var)
            
        print(f"   Config value: {str(config_value)[:20]}{'...' if len(str(config_value)) > 20 else ''}")
        print()
    
    # Check optional variables
    print("OPTIONAL ENVIRONMENT VARIABLES:")
    print("-" * 40)
    missing_optional = []
    
    for var in optional_vars:
        value = os.environ.get(var)
        if hasattr(config, var):
            config_value = getattr(config, var)
        else:
            config_value = 'NOT IN CONFIG'
            
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️  {var}: NOT SET (using default)")
            missing_optional.append(var)
            
        print(f"   Config value: {config_value}")
        print()
    
    # Summary
    print("="*60)
    print("SUMMARY")
    print("="*60)
    
    if missing_critical:
        print(f"❌ CRITICAL ISSUES: {len(missing_critical)} missing critical variables:")
        for var in missing_critical:
            print(f"   - {var}")
        print()
        
    if missing_optional:
        print(f"⚠️  WARNINGS: {len(missing_optional)} missing optional variables:")
        for var in missing_optional:
            print(f"   - {var}")
        print()
    
    if not missing_critical:
        print("✅ All critical environment variables are set!")
        
    print(f"📁 Model path: {config.MODEL_PATH}")
    print(f"🔑 API Key: {'SET' if config.API_KEY else 'NOT SET'}")
    print(f"🗺️  Google Maps API Key: {'SET' if hasattr(config, 'GOOGLE_API_KEY') and config.GOOGLE_API_KEY else 'NOT SET'}")
    
    return len(missing_critical) == 0

if __name__ == "__main__":
    success = test_environment_variables()
    sys.exit(0 if success else 1)
