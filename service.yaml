apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: solar-classifier
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # HTTP/2 end-to-end configuration
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        autoscaling.knative.dev/maxScale: "100"
        autoscaling.knative.dev/minScale: "0"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/SERVICE_NAME
        ports:
        - name: http1
          containerPort: 8080
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: User-Agent
              value: "GoogleHC/1.0"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
  traffic:
  - percent: 100
    latestRevision: true
