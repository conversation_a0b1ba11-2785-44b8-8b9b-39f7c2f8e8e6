"""
WSGI entry point for production deployment
"""
import os
import sys
import logging
from pathlib import Path

# Add the project directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set production environment
os.environ.setdefault('FLASK_ENV', 'production')

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    # Import and initialize the Flask app
    from app.api import app, load_model
    
    # Load the model on startup
    logger.info("Starting model loading...")
    if not load_model():
        logger.error("Failed to load model. Service will start but may not function properly.")
        # Don't raise error - let the service start and handle errors gracefully
    else:
        logger.info("Model loaded successfully!")
        
except Exception as e:
    logger.error(f"Error during startup: {e}")
    logger.error("Service will start but may not function properly.")
    # Import app without model loading
    try:
        from app.api import app
        logger.info("App imported successfully without model loading")
    except ImportError as import_error:
        logger.error(f"Failed to import app: {import_error}")
        # Create a minimal app as fallback
        from flask import Flask
        app = Flask(__name__)
        app.config['TESTING'] = True
        
        @app.route('/health')
        def health():
            return {'status': 'degraded', 'error': 'App import failed'}

# WSGI application object - ensure it's always defined
application = app

if __name__ == "__main__":
    application.run()